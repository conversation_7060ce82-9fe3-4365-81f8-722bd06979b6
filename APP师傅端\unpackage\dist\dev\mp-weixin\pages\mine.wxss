@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* Login Popup Styles */
.login-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}
.login-popup {
  background-color: #fff;
  width: 100%;
  border-radius: 40rpx 40rpx 0 0;
  position: relative;
  max-height: 60vh;
  padding-bottom: 10rpx;
  -webkit-animation: slideUp 0.3s ease-out;
          animation: slideUp 0.3s ease-out;
}
@-webkit-keyframes slideUp {
from {
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
}
to {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes slideUp {
from {
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
}
to {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
.close-btn {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 40rpx;
  z-index: 10;
}
.popup-content {
  padding: 80rpx 60rpx 40rpx;
  text-align: center;
}
.welcome-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.welcome-subtitle {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 80rpx;
}
.agreement-section {
  margin-bottom: 60rpx;
  display: flex;
  justify-content: center;
}
.checkbox-container {
  display: flex;
  align-items: flex-start;
  text-align: left;
  max-width: 560rpx;
}
.checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #ddd;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  margin-top: 4rpx;
  flex-shrink: 0;
  background-color: #fff;
  transition: all 0.2s;
}
.checkbox.checked {
  background-color: #00C853;
  border-color: #00C853;
  color: #fff;
}
.checkbox .iconfont {
  font-size: 24rpx;
}
.agreement-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}
.agreement-text .link {
  color: #00C853;
}
.phone-login-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #00C853, #4CAF50);
  border: none;
  border-radius: 50rpx;
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(0, 200, 83, 0.3);
  transition: all 0.2s;
}
.phone-login-btn:active:not(.disabled) {
  -webkit-transform: translateY(2rpx);
          transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 200, 83, 0.3);
}
.phone-login-btn.disabled {
  background: #ccc;
  box-shadow: none;
  opacity: 0.6;
}
.phone-login-btn::after {
  border: none;
}
.bind-phone-container {
  margin-top: 10rpx;
}
.bind-phone-btn {
  background: none;
  border: 2rpx solid rgba(255, 255, 255, 0.8);
  border-radius: 32rpx;
  color: #fff;
  font-size: 28rpx;
  line-height: 1.5;
  padding: 8rpx 24rpx;
  transition: all 0.2s;
}
.bind-phone-btn:active:not([disabled]) {
  background: rgba(255, 255, 255, 0.1);
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.bind-phone-btn[disabled] {
  opacity: 0.6;
}
.bind-phone-btn::after {
  border: none;
}
.input-group {
  margin-bottom: 40rpx;
}
.input-group .input-item {
  display: flex;
  align-items: center;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  min-height: 88rpx;
  padding: 0;
  transition: all 0.3s ease;
}
.input-group .input-item:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 6rpx rgba(59, 130, 246, 0.08);
}
.input-group .input-item .input-icon {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 24rpx;
  background: rgba(59, 130, 246, 0.08);
  border-radius: 50%;
  flex-shrink: 0;
}
.input-group .input-item .input-field {
  flex: 1;
  margin-left: 24rpx;
  margin-right: 16rpx;
  font-size: 32rpx;
  color: #1e293b;
  background: transparent;
  border: none;
  outline: none;
}
.input-group .input-item .input-field::-webkit-input-placeholder {
  color: #94a3b8;
}
.input-group .input-item .input-field::placeholder {
  color: #94a3b8;
}
.input-group .input-item .sms-btn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: #fff;
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  margin-right: 16rpx;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.2);
  flex-shrink: 0;
}
.input-group .input-item .sms-btn.disabled {
  background: #cbd5e1;
  color: #64748b;
  box-shadow: none;
}
.input-group .input-item .sms-btn:not(.disabled):active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.alternative-login {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}
.alternative-login .divider-line {
  flex: 1;
  height: 1rpx;
  background-color: #eee;
}
.alternative-login .divider-text {
  font-size: 26rpx;
  color: #999;
  margin: 0 30rpx;
}
.sms-login-btn {
  width: 100%;
  height: 88rpx;
  background-color: #fff;
  border: 2rpx solid #ddd;
  border-radius: 44rpx;
  color: #666;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}
.sms-login-btn:active {
  background-color: #f8f8f8;
  border-color: #00C853;
}
.sms-login-btn::after {
  border: none;
}
.sms-login-btn .iconfont {
  margin-right: 16rpx;
  font-size: 36rpx;
}
/* Floating Contact Button Styles */
.floating-contact {
  position: fixed;
  bottom: 150rpx;
  right: 30rpx;
  z-index: 1000;
  background-color: #fff;
  border-radius: 50rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  padding: 10rpx 20rpx;
  display: flex;
  align-items: center;
}
.contact-container {
  display: flex;
  align-items: center;
}
.contact-btn {
  background: none;
  border: none;
  color: #576b95;
  font-size: 30rpx;
  line-height: 1.5;
  padding: 10rpx 20rpx;
  display: flex;
  align-items: center;
}
.contact-btn:active {
  background-color: #ededee;
}
/* Existing Styles */
.pages-mine {
  background-color: #f8f8f8;
  min-height: 100vh;
  padding-bottom: 120rpx;
}
.pages-mine .header {
  height: 292rpx;
  background-color: #599EFF;
  position: relative;
}
.pages-mine .header .header-content {
  display: flex;
  align-items: center;
  padding: 40rpx 30rpx 0;
}
.pages-mine .header .header-content .avatar_view {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
.pages-mine .header .header-content .avatar_view .avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.pages-mine .header .header-content .user-info {
  flex: 1;
  margin-left: 20rpx;
  color: #fff;
}
.pages-mine .header .header-content .user-info .user-info-logged {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}
.pages-mine .header .header-content .user-info .nickname {
  font-size: 36rpx;
  font-weight: bold;
}
.pages-mine .header .header-content .user-info .phone-number {
  font-size: 28rpx;
  opacity: 0.9;
}
.pages-mine .header .header-content .user-info button {
  background: none;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  border-radius: 32rpx;
  color: #fff;
  font-size: 32rpx;
  line-height: 1.5;
  padding: 10rpx 30rpx;
}
.pages-mine .header .header-content .user-info button.loading {
  opacity: 0.7;
}
.pages-mine .header .header-content .user-info button::after {
  border: none;
}
.pages-mine .header .header-content .user-info .status-info-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-top: 10rpx;
  flex-wrap: wrap;
}
.pages-mine .header .header-content .user-info .status-badge {
  display: inline-block;
  padding: 8rpx 20rpx;
  font-size: 24rpx;
  line-height: 1.2;
  border-radius: 20rpx;
  color: #fff;
  text-align: center;
  width: -webkit-fit-content;
  width: fit-content;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  cursor: pointer;
}
.pages-mine .header .header-content .user-info .status-not-registered {
  background-color: #b0b0b0;
}
.pages-mine .header .header-content .user-info .status-pending {
  background-color: #f4b400;
}
.pages-mine .header .header-content .user-info .status-approved {
  background-color: #f5a623;
}
.pages-mine .header .header-content .user-info .status-rejected {
  background-color: #f44336;
}
.pages-mine .header .header-content .user-info .master-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 22rpx;
}
.pages-mine .header .header-content .user-info .credit-info-badge {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 6rpx 12rpx;
  background: linear-gradient(135deg, #ff6b35, #ff8c42);
  border-radius: 16rpx;
  color: #fff;
  font-size: 20rpx;
  box-shadow: 0 2rpx 6rpx rgba(255, 107, 53, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}
.pages-mine .header .header-content .user-info .credit-info-badge:hover {
  -webkit-transform: translateY(-1rpx);
          transform: translateY(-1rpx);
  box-shadow: 0 4rpx 8rpx rgba(255, 107, 53, 0.4);
}
.pages-mine .header .header-content .user-info .credit-info-badge .credit-label {
  font-size: 18rpx;
  opacity: 0.9;
}
.pages-mine .header .header-content .user-info .credit-info-badge .credit-value {
  font-weight: bold;
  font-size: 20rpx;
}
.pages-mine .header .header-content .user-info .star-info-badge {
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 6rpx 12rpx;
  background: linear-gradient(135deg, #ffa500, #ffb84d);
  border-radius: 16rpx;
  color: #fff;
  font-size: 20rpx;
  box-shadow: 0 2rpx 6rpx rgba(255, 165, 0, 0.3);
}
.pages-mine .header .header-content .user-info .star-info-badge .star-icon {
  font-size: 18rpx;
}
.pages-mine .header .header-content .user-info .star-info-badge .star-value {
  font-weight: bold;
  font-size: 20rpx;
}
.pages-mine .header .header-content .settings {
  padding: 10rpx;
}
.pages-mine .header .header-content .settings .icon-xitong {
  font-size: 40rpx;
  color: #fff;
}
.pages-mine .box1 {
  margin-top: -20rpx;
  border-radius: 36rpx 36rpx 0 0;
  position: relative;
  z-index: 10;
}
.pages-mine .mine-menu-list {
  background-color: #fff;
  margin: 0 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
}
.pages-mine .mine-menu-list .menu-title {
  height: 90rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx 0 40rpx;
  border-bottom: 1px solid #f0f0f0;
}
.pages-mine .mine-menu-list .menu-title .f-paragraph {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}
.pages-mine .mine-menu-list .flex-warp {
  display: flex;
  flex-wrap: wrap;
  padding: 30rpx 0;
}
.pages-mine .mine-menu-list .flex-warp .order-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 25%;
  font-size: 25rpx;
  margin-top: 20rpx;
  color: #666;
  transition: -webkit-transform 0.2s;
  transition: transform 0.2s;
  transition: transform 0.2s, -webkit-transform 0.2s;
}
.pages-mine .mine-menu-list .flex-warp .order-item:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.pages-mine .mine-menu-list .flex-warp .order-item .icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
.pages-mine .mine-menu-list .flex-warp .order-item .number-circle {
  position: absolute;
  top: -10rpx;
  right: -5rpx;
  width: 30rpx;
  height: 30rpx;
  background-color: #ff4d4f;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
}
.pages-mine .mine-menu-list .flex-warp .mt-sm {
  margin-top: 16rpx;
}
.pages-mine .spacer {
  height: 20rpx;
  background-color: transparent;
}
.pages-mine .mine-tool-grid {
  background-color: #fff;
  margin: 0 20rpx 30rpx;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  padding: 30rpx;
}
.pages-mine .mine-tool-grid .grid-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 20rpx;
}
.pages-mine .mine-tool-grid .grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: calc(33.33% - 20rpx);
  min-width: 140rpx;
  transition: -webkit-transform 0.2s ease;
  transition: transform 0.2s ease;
  transition: transform 0.2s ease, -webkit-transform 0.2s ease;
}
.pages-mine .mine-tool-grid .grid-item:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.pages-mine .mine-tool-grid .grid-item .grid-icon-container {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.pages-mine .mine-tool-grid .grid-item .grid-icon-container.switch-identity {
  /* Specific styling for switch-identity icon */
}
.pages-mine .mine-tool-grid .grid-item .grid-text {
  font-size: 25rpx;
  color: #333;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
  margin-bottom: 8rpx;
}
.pages-mine .mine-tool-grid .grid-item .contact-btn-wrapper {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  line-height: 1;
}
.pages-mine .mine-tool-grid .grid-item .contact-btn-wrapper::after {
  border: none;
}
.pages-mine .flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
/* 驳回原因弹窗样式 */
.reject-reason-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.reject-reason-popup {
  background-color: #fff;
  width: 80%;
  max-width: 600rpx;
  border-radius: 20rpx;
  position: relative;
  -webkit-animation: fadeIn 0.3s ease-out;
          animation: fadeIn 0.3s ease-out;
}
@-webkit-keyframes fadeIn {
from {
    opacity: 0;
    -webkit-transform: scale(0.9);
            transform: scale(0.9);
}
to {
    opacity: 1;
    -webkit-transform: scale(1);
            transform: scale(1);
}
}
@keyframes fadeIn {
from {
    opacity: 0;
    -webkit-transform: scale(0.9);
            transform: scale(0.9);
}
to {
    opacity: 1;
    -webkit-transform: scale(1);
            transform: scale(1);
}
}
.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 30rpx;
}
.reject-reason-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  margin-bottom: 30rpx;
  min-height: 100rpx;
}
.confirm-btn {
  width: 100%;
  height: 80rpx;
  background-color: #448cfb;
  color: #fff;
  border: none;
  border-radius: 10rpx;
  font-size: 28rpx;
  font-weight: bold;
}
.confirm-btn::after {
  border: none;
}

