<template>
	<view class="pages-mine">
		<view class="header">
			<view class="header-content">
				<view class="avatar_view">
					<image mode="aspectFill" class="avatar"
						:src="displayUserInfo.avatarUrl || '/static/mine/default_user.png'"></image>
				</view>
				<view class="user-info">
					<view v-if="!isLoggedIn">
						<button @click="showLoginPopup" :disabled="isLoading" :class="{ 'loading': isLoading }">
							{{ isLoading ? '登录中...' : '用户登录' }}
						</button>
					</view>
					<view v-else class="user-info-logged">
						<view class="nickname">
							{{ displayUserInfo.nickName }}
						</view>
						<view v-if="displayUserInfo.phone" class="phone-number">{{ displayUserInfo.phone }}</view>
						<view v-else class="bind-phone-container">
							<button @click="showBindPhonePopup" class="bind-phone-btn" :disabled="isBindingPhone">
								{{ isBindingPhone ? '绑定中...' : '绑定手机号' }}
							</button>
						</view>
						<view class="status-info-container">
							<view class="status-badge" :class="statusBadgeClass" @click="handleStatusClick">
								{{ statusText }}
								{{ labelName }}
							</view>
							<view class="master-info" v-if="isLoggedIn && shifustatus !== -1">
								<view @click="gocredit" class="credit-info-badge">
									<text class="credit-label">信用分</text>
									<text class="credit-value">{{ credit }}</text>
								</view>
								<view @click="goGrade" class="star-info-badge">
									<text class="star-icon">师傅等级</text>
									<text class="star-value">{{ starRating }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view @click="navigateTo('../user/userProfile')" class="settings">
					<i class="iconfont icon-xitong text-bold"></i>
				</view>
			</view>
		</view>

		<view class="mine-menu-list box-shadow fill-base box1">
			<view class="menu-title flex-between pl-lg pr-md b-1px-b">
				<view class="f-paragraph c-title text-bold">我的订单</view>
			</view>
			<view class="flex-warp pt-lg pb-lg">
				<view class="order-item" v-for="(item, index) in orderList" :key="index" @tap="navigateTo(item.url)">
					<view class="icon-container">
						<u-icon :name="item.icon" color="#448cfb" size="28"></u-icon>
						<view v-if="item.count > 0" class="number-circle">{{ item.count }}</view>
					</view>
					<view class="mt-sm">{{ item.text }}</view>
				</view>
			</view>
		</view>

		<view class="mine-menu-list box-shadow fill-base">
			<view class="menu-title flex-between pl-lg pr-md b-1px-b">
				<view class="f-paragraph c-title text-bold">常用功能</view>
			</view>
			<view class="flex-warp pt-lg pb-lg">
				<view class="order-item" v-for="(item, index) in orderList3" :key="index"
					@tap="handleNavigate(item.url)">
					<u-icon :name="item.icon" color="#448cfb" size="28"></u-icon>
					<view class="mt-sm">{{ item.text }}</view>
				</view>
			</view>
		</view>

		<view class="mine-menu-list box-shadow fill-base">
			<view class="menu-title flex-between pl-lg pr-md b-1px-b">
				<view class="f-paragraph c-title text-bold">其他功能</view>
			</view>
			<view class="flex-warp pt-lg pb-lg">
				<view class="order-item" @tap="handleNavigate('/shifu/skills')">
					<u-icon name="plus-square-fill" color="#448cfb" size="28"></u-icon>
					<view class="mt-sm">技能标签</view>
				</view>
				<view class="order-item" @tap="handleNavigate('/shifu/Professiona')">
					<u-icon name="order" color="#448cfb" size="28"></u-icon>
					<view class="mt-sm">技能证书</view>
				</view>
				<view class="order-item" @tap="handleNavigate('/shifu/coreWallet')">
					<u-icon name="rmb-circle-fill" color="#448cfb" size="28"></u-icon>
					<view style="color: #448cfb;" class="mt-sm">提现管理</view>
				</view>
				<view class="order-item" @tap="handleNavigate('/user/promotion')">
					<u-icon name="red-packet-fill" color="#E41F19" size="28"></u-icon>
					<view style="color: #E41F19;" class="mt-sm">邀请有礼</view>
				</view>

			</view>
		</view>

		<view class="spacer"></view>

		<view class="mine-tool-grid fill-base">
			<view class="grid-container">
				<view class="grid-item" v-for="(item, index) in toolList2" :key="index" @tap="handleNavigate(item.url)">
					<view class="grid-icon-container">
						<u-icon :name="item.icon" :color="item.iconColor" size="28"></u-icon>
					</view>
					<view class="grid-text">{{ item.text }}</view>
				</view>

				<!-- <view class="grid-item" @tap="navigateTo('../pages/service')">
					<view class="grid-icon-container switch-identity">
						<u-icon name="man-add" color="#E41F19" size="28"></u-icon>
					</view>
					<view style="color: #E41F19;" class="grid-text">切换用户版</view>
				</view> -->

				<view class="grid-item">
					<!-- #ifdef MP-WEIXIN -->
					<button class="contact-btn-wrapper" open-type="contact" bindcontact="handleContact"
						session-from="sessionFrom">
						<view class="grid-icon-container switch-identity">
							<u-icon name="server-man" color="#448cfb" size="28"></u-icon>
						</view>
						<view class="grid-text">客服</view>
					</button>
					<!-- #endif -->
					<!-- #ifdef APP-PLUS -->
					<view class="contact-btn-wrapper" @tap="callCustomerService">
						<view class="grid-icon-container switch-identity">
							<u-icon name="server-man" color="#448cfb" size="28"></u-icon>
						</view>
						<view class="grid-text">客服</view>
					</view>
					<!-- #endif -->
				</view>
			</view>
		</view>

		<!-- <view class="floating-contact">
			<view class="contact-container">
				<u-icon name="server-man" color="#576b95" size="24"></u-icon>
				<button class="contact-btn" open-type="contact" bindcontact="handleContact" session-from="sessionFrom">
					客服
				</button>
			</view>
		</view> -->

		<view v-if="loginPopupVisible" class="login-popup-overlay" @tap="hideLoginPopup">
			<view class="login-popup" @tap.stop>
				<view class="close-btn" @tap="hideLoginPopup">
					<i class="iconfont icon-close"></i>
				</view>

				<view class="popup-content">
					<view class="welcome-title">欢迎登录今师傅</view>
					<view class="welcome-subtitle">登录后即可享受完整服务</view>

					<view class="agreement-section">
						<view class="checkbox-container" @tap="toggleAgreement">
							<view class="checkbox" :class="{ 'checked': agreedToTerms }">
								<i v-if="agreedToTerms" class="iconfont icon-check">✓</i>
							</view>
							<view class="agreement-text">
								我已阅读并同意 <text class="link" @tap.stop="navigateToAgreement('service')">《今师傅服务协议》</text>
								<text class="link" @tap.stop="navigateToAgreement('privacy')">《隐私政策》</text>
							</view>
						</view>
					</view>

					<button class="phone-login-btn" :class="{ 'disabled': !agreedToTerms || isLoading }"
						:disabled="!agreedToTerms || isLoading" open-type="getPhoneNumber"
						@getphonenumber="onGetPhoneNumber">
						{{ isLoading ? '登录中...' : '手机号快捷登录' }}
					</button>
				</view>
			</view>
		</view>

		<!-- Bind Phone Popup -->
		<view v-if="bindPhonePopupVisible" class="login-popup-overlay" @tap="hideBindPhonePopup">
			<view class="login-popup" @tap.stop>
				<!-- Close Button -->
				<view class="close-btn" @tap="hideBindPhonePopup">
					<i class="iconfont icon-close"></i>
				</view>

				<!-- Popup Content -->
				<view class="popup-content">
					<view class="welcome-title">绑定手机号</view>
					<view class="welcome-subtitle">绑定手机号后可享受完整服务</view>

					<!-- Phone Input -->
					<view class="input-group">
						<view class="input-item">
							<view class="input-icon">
								<u-icon name="phone" color="#3b82f6" size="18"></u-icon>
							</view>
							<input class="input-field" type="number" placeholder="请输入手机号" v-model="bindPhoneForm.phone"
								maxlength="11" />
						</view>
						<view class="input-item">
							<view class="input-icon">
								<u-icon name="chat" color="#3b82f6" size="18"></u-icon>
							</view>
							<input class="input-field" type="number" placeholder="请输入验证码" v-model="bindPhoneForm.code"
								maxlength="6" />
							<view class="sms-btn" @click="sendBindPhoneSmsCode"
								:class="{ disabled: bindPhoneSmsCountdown > 0 }">
								{{ bindPhoneSmsCountdown > 0 ? `${bindPhoneSmsCountdown}s` : '获取验证码' }}
							</view>
						</view>
					</view>

					<!-- Bind Button -->
					<button class="phone-login-btn" :class="{ 'disabled': !canBindPhone || isBindingPhone }"
						:disabled="!canBindPhone || isBindingPhone" @click="handleBindPhone">
						{{ isBindingPhone ? '绑定中...' : '绑定手机号' }}
					</button>
				</view>
			</view>
		</view>

		<!-- 驳回原因弹窗 -->
		<view v-if="rejectReasonVisible" class="reject-reason-overlay" @tap="hideRejectReason">
			<view class="reject-reason-popup" @tap.stop>
				<view class="close-btn" @tap="hideRejectReason">
					<i class="iconfont icon-close"></i>
				</view>
				<view class="popup-content">
					<view class="popup-title">审核驳回原因</view>
					<view class="reject-reason-text">{{ shText || '暂无驳回原因说明' }}</view>
					<button class="confirm-btn" @tap="hideRejectReason">我知道了</button>
				</view>
			</view>
		</view>

		<tabbar cur="1"></tabbar>
	</view>
</template>

<script>
import tabbar from "@/components/tabbarsf.vue";
import {
	mapState,
	mapMutations
} from "vuex";
import locationManager from '@/utils/location-manager.js';

// Utility function for debouncing
const debounce = (func, wait) => {
	let timeout;
	return function (...args) {
		const context = this;
		clearTimeout(timeout);
		timeout = setTimeout(() => func.apply(context, args), wait);
	};
};

export default {
	components: {
		tabbar
	},
	data() {
		return {
			isLoading: false, // 确保初始状态不是loading
			inviteCode: '',
			tmplIds: [
				' vR1qJM-SEYbGnvXdl4HQ5D2Nf7USnBgcmeov8slExOo',
				'HVNlAWjUm-wjtFxYizNdqzPvrYvofmysaXs_iZ0T1Gs',
				'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'
			],
			code: '',
			labelName: '',
			loginPopupVisible: false,
			agreedToTerms: false,
			shifustatus: -1, // Initialize with a default numeric value
			// 师傅详细信息
			shText: '', // 驳回原因
			credit: 1, // 信用分
			starRating: '一星', // 师傅星级
			// 驳回原因弹窗
			rejectReasonVisible: false,
			// 绑定手机号相关
			bindPhonePopupVisible: false,
			isBindingPhone: false,
			bindPhoneSmsCountdown: 0,
			bindPhoneSmsTimer: null,
			bindPhoneForm: {
				phone: '',
				code: ''
			},
			orderList: [{
				icon: 'order',
				text: '全部',
				url: '/shifu/master_my_order?tab=0',
				count: 0
			},
			{
				icon: 'bell',
				text: '待上门',
				url: '/shifu/master_my_order?tab=3',
				count: 0
			},
			{
				icon: 'hourglass-half-fill',
				text: '待服务',
				url: '/shifu/master_my_order?tab=5',
				count: 0
			},
			{
				icon: 'clock',
				text: '服务中',
				url: '/shifu/master_my_order?tab=6',
				count: 0
			},
			{
				icon: 'thumb-up',
				text: '已完成',
				url: '/shifu/master_my_order?tab=7',
				count: 0
			},
			{
				icon: 'chat-fill',
				text: '售后',
				url: '/shifu/master_my_order?tab=8',
				count: 0
			},
			],
			orderList3: [{
				icon: 'red-packet',
				text: '服务收入',
				url: '/shifu/income'
			},
			{
				icon: 'file-text-fill',
				text: '报价列表',
				url: '/shifu/master_bao_list'
			},
			{
				icon: 'rmb-circle',
				text: '保证金',
				url: '/shifu/Margin'
			}
			// ,
			// {
			// 	icon: 'account-fill',
			// 	text: '师傅等级',
			// 	url: '/shifu/shifuGrade'
			// }
			],
			toolList2: [{
				icon: 'plus-people-fill',
				text: '师傅入驻',
				url: '/shifu/Settle',
				iconColor: '#448cfb'
			},
			{
				icon: 'edit-pen',
				text: '编辑师傅资料',
				url: '/shifu/master_Info',
				iconColor: '#448cfb'
			}
			]
		};
	},
	computed: {
		...mapState({
			// Changed from storeUserInfo to userInfo based on user's request and Vuex module
			userInfo: state => state.user.userInfo || {},
			token: state => state.user.autograph || '',
			erweima: state => state.user.erweima || '',
			regeocode: (state) => state.service.regeocode,
		}),
		isLoggedIn() {
			// 如果有token，就认为已登录，即使用户信息还在加载中
			// 这样可以避免在微信模拟器中因为用户信息加载延迟导致的登录状态丢失
			const hasToken = !!this.token;

			// 只在调试模式下打印日志，避免过多的控制台输出
			if (process.env.NODE_ENV === 'development') {
				console.log('isLoggedIn检查:', {
					hasToken,
					result: hasToken
				});
			}

			return hasToken;
		},
		// This computed property will combine Vuex userInfo with local storage `shiInfo`
		// for display purposes, giving `shiInfo` priority where it makes sense.
		displayUserInfo() {
			const shiInfo = uni.getStorageSync('shiInfo') ? JSON.parse(uni.getStorageSync('shiInfo')) : {};

			console.log('displayUserInfo 计算开始:', {
				isLoggedIn: this.isLoggedIn,
				vuexUserInfo: this.userInfo,
				shiInfo: shiInfo,
				token: this.token
			});

			// 优先使用有效的师傅信息，如果师傅信息为空则使用用户信息
			const avatarUrl = (shiInfo.avatarUrl && shiInfo.avatarUrl.trim()) ? shiInfo.avatarUrl :
				(this.userInfo.avatarUrl || uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png');

			const nickName = (shiInfo.coachName && shiInfo.coachName.trim()) ? shiInfo.coachName :
				(this.userInfo.nickName || uni.getStorageSync('nickName') || '微信用户');

			const phone = (shiInfo.mobile && shiInfo.mobile.trim()) ? shiInfo.mobile :
				(this.userInfo.phone || uni.getStorageSync('phone') || '');

			const userId = shiInfo.userId || this.userInfo.userId || uni.getStorageSync('userId') || '';
			const pid = shiInfo.pid || this.userInfo.pid || uni.getStorageSync('pid') || '';
			const shifuId = shiInfo.shifuId || '';

			const result = {
				phone: this.isLoggedIn ? phone : '',
				avatarUrl: this.isLoggedIn ? avatarUrl : '/static/mine/default_user.png',
				nickName: this.isLoggedIn ? nickName : '微信用户',
				userId: this.isLoggedIn ? userId : '',
				shifuId: this.isLoggedIn ? shifuId : '',
				pid: this.isLoggedIn ? pid : ''
			};

			console.log('displayUserInfo计算结果:', result);
			return result;
		},
		statusText() {
			console.log('statusText 计算开始:', {
				isLoggedIn: this.isLoggedIn,
				shifustatus: this.shifustatus,
				cachedShiInfo: uni.getStorageSync('shiInfo')
			});

			// 如果用户未登录，不显示状态信息
			if (!this.isLoggedIn) {
				console.log('用户未登录，返回空状态');
				return '';
			}

			// 优先从本地缓存获取状态，提高响应速度
			const cachedShiInfo = uni.getStorageSync('shiInfo');
			let status = this.shifustatus;

			if (cachedShiInfo) {
				try {
					const parsedShiInfo = JSON.parse(cachedShiInfo);
					if (parsedShiInfo.status !== undefined && parsedShiInfo.status !== null) {
						status = parsedShiInfo.status;
						// 如果本地状态与当前状态不一致，更新当前状态
						if (this.shifustatus !== status) {
							this.shifustatus = status;
							console.log('从缓存同步师傅状态:', status);
						}
					}
				} catch (error) {
					console.error('解析本地师傅信息失败:', error);
				}
			}

			// 确保状态是数字类型
			status = Number(status);
			console.log('最终使用的状态值:', status);

			// 如果状态仍然无效，设置默认状态并触发获取（仅在已登录时）
			if (isNaN(status) || status === undefined || status === null) {
				console.log('检测到无效状态，设置为默认状态');
				status = -1; // 默认为未入驻

				// 只有在已登录时才触发获取师傅信息
				if (this.isLoggedIn && !this._fetchingStatus) {
					console.log('用户已登录，触发获取师傅信息');
					this._fetchingStatus = true;
					this.$nextTick(() => {
						this.fetchShifuInfoImmediately().finally(() => {
							this._fetchingStatus = false;
						});
					});
				}
			}

			let statusTextResult = '';
			switch (status) {
				case -1:
					statusTextResult = '未入驻师傅';
					break;
				case 1:
					statusTextResult = '审核中';
					break;
				case 2:
					statusTextResult = '已认证';
					break;
				case 4:
					statusTextResult = '审核驳回';
					break;
				default:
					console.log('未知的师傅状态值:', status);
					statusTextResult = '未入驻师傅'; // 默认显示未入驻
			}

			console.log('statusText 计算结果:', statusTextResult);
			return statusTextResult;
		},
		statusBadgeClass() {
			return {
				'status-not-registered': this.shifustatus === -1,
				'status-pending': this.shifustatus === 1,
				'status-approved': this.shifustatus === 2,
				'status-rejected': this.shifustatus === 4
			};
		},
		canBindPhone() {
			return this.bindPhoneForm.phone && this.bindPhoneForm.code &&
				this.validatePhone(this.bindPhoneForm.phone);
		}
	},
	watch: {
		// Watch for changes in the Vuex userInfo and trigger updates
		userInfo: {
			handler(newVal, oldVal) {
				// Only update if there's a meaningful change to avoid infinite loops
				if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
					this.saveUserInfoToStorage(newVal);
					// Force update if necessary, though Vue's reactivity should handle most cases
					this.$nextTick(() => {
						this.$forceUpdate();
					});
				}
			},
			deep: true, // Watch for nested property changes
			immediate: true // Run the handler immediately on component mount
		}
	},
	onLoad(options) {
		this.getNowPosition();
		// 先从缓存加载 labelName，避免显示延迟
		this.loadCachedLabelName();
		// 加载缓存的师傅详细信息
		this.loadCachedMasterInfo();
		this.getmyGrade();
		if (options.inviteCode) {
			console.log('Received inviteCode:', options.inviteCode);
			this.inviteCode = options.inviteCode;
			uni.setStorageSync('receivedInviteCode', options.inviteCode);
		}
		if (this.erweima) {
			console.log('erweima from Vuex:', this.erweima);
			this.inviteCode = this.erweima;
			uni.setStorageSync('receivedInviteCode', this.erweima);
		} else {
			const erweima = uni.getStorageSync('erweima');
			if (erweima) {
				console.log('erweima from storage:', erweima);
				this.$store.commit('setErweima', erweima);
				this.inviteCode = erweima;
				uni.setStorageSync('receivedInviteCode', erweima);
			}
		}

		// 初始化用户数据
		this.initUserData();
		// #ifdef MP-WEIXIN
		uni.login({
			provider: 'weixin',
			success: res => {
				if (res.code) {
					this.code = res.code;
					console.log('Initial wx.login code:', this.code);
				}
			},
			fail: err => {
				console.error('wx.login failed:', err);
			}

		});
		// #endif
		// 只有在确认有token时才进行后续操作，避免不必要的loading状态
		if (this.token) {
			if (this.isLoggedIn) {
				this.debounceGetHighlight();
			}
			// 延迟获取师傅信息，确保用户数据已经加载完成
			this.$nextTick(() => {
				this.fetchShifuInfo();
			});
		}
	},
	onShow() {
		console.log('=== Mine页面onShow调试信息 ===');
		console.log('token:', this.token);
		console.log('isLoggedIn:', this.isLoggedIn);
		console.log('userInfo from Vuex:', this.userInfo);
		console.log('shiInfo from storage:', uni.getStorageSync('shiInfo'));
		console.log('displayUserInfo:', this.displayUserInfo);

		// 确保初始状态不是loading
		this.isLoading = false;

		// 检查是否有token（从 Vuex 和本地存储双重检查）
		const storageToken = uni.getStorageSync('token');
		const vuexToken = this.token;

		console.log('Token 检查:', {
			vuexToken,
			storageToken,
			hasAnyToken: !!(vuexToken || storageToken)
		});

		// 如果没有任何token，直接清除登录状态
		if (!vuexToken && !storageToken) {
			console.log('没有任何token，清除登录状态');
			this.handleInvalidSession();
			return;
		}

		// 如果本地存储有token但Vuex没有，同步到Vuex
		if (storageToken && !vuexToken) {
			console.log('同步本地存储的token到Vuex');
			this.updateUserItem({
				key: 'autograph',
				val: storageToken
			});
		}

		// 每次显示页面时都先加载缓存的 labelName
		this.loadCachedLabelName();
		// 加载缓存的师傅详细信息
		this.loadCachedMasterInfo();

		// 如果有token但用户信息不完整，尝试初始化用户数据
		const hasUserInfo = !!(this.userInfo.userId || this.userInfo.phone);
		if (this.isLoggedIn && !hasUserInfo) {
			console.log('有token但用户信息未完整加载，尝试初始化用户数据');
			this.initUserData();
			// 给一点时间让数据初始化完成，但不显示loading状态
			setTimeout(() => {
				const hasUserInfoAfterInit = !!(this.userInfo.userId || this.userInfo.phone);
				if (this.isLoggedIn && hasUserInfoAfterInit) {
					this.loadUserRelatedData();
				}
			}, 100);
		} else if (this.isLoggedIn && hasUserInfo) {
			// 已登录且有用户信息，直接加载用户相关数据
			this.loadUserRelatedData();
		}

		this.$nextTick(() => {
			this.$forceUpdate();
		});
	},
	onPullDownRefresh() {
		// Handle pull-down refresh
		if (this.isLoggedIn && this.token) {
			Promise.all([
				this.fetchUserInfo(),
				this.getHighlight(),
				this.fetchShifuInfo(),
				this.getmyGrade() // 添加师傅等级刷新
			]).then(() => {
				uni.stopPullDownRefresh();
				// this.showToast('刷新成功', 'success');
			}).catch(err => {
				console.error('Pull-down refresh failed:', err);
				uni.stopPullDownRefresh();
			});
		} else {
			// If not logged in, reset UI and stop refresh
			this.handleInvalidSession();
			uni.stopPullDownRefresh();
			this.showToast('请先登录');
		}
	},
	methods: {
		// 从缓存加载 labelName，避免显示延迟
		loadCachedLabelName() {
			const cachedLabelName = uni.getStorageSync('labelName');
			if (cachedLabelName) {
				this.labelName = cachedLabelName;
				console.log('从缓存加载 labelName:', cachedLabelName);
			}
		},
gocredit(){
	// 跳转到信用分页面
	uni.navigateTo({
		url: '/shifu/creditInfo',
		fail: (err) => {
			console.error('跳转信用分页面失败:', err);
			this.showToast('页面跳转失败，请重试');
		}
	});
},
goGrade(){
	// 跳转到师傅等级页面
	uni.navigateTo({
		url: '/shifu/shifuGrade',
		fail: (err) => {
			console.error('跳转师傅等级页面失败:', err);
			this.showToast('页面跳转失败，请重试');
		}
	});
},
		// 从缓存加载师傅详细信息
		loadCachedMasterInfo() {
			const cachedShiInfo = uni.getStorageSync('shiInfo');
			if (cachedShiInfo) {
				try {
					const parsedShiInfo = JSON.parse(cachedShiInfo);
					this.shText = parsedShiInfo.shText || '';
					this.credit = parsedShiInfo.credit || 1;
					this.starRating = parsedShiInfo.starRating || '一星';
					console.log('从缓存加载师傅详细信息:', {
						shText: this.shText,
						credit: this.credit,
						starRating: this.starRating
					});
				} catch (error) {
					console.error('解析缓存的师傅详细信息失败:', error);
				}
			}
		},

		// 处理状态徽章点击事件
		handleStatusClick() {
			// 只有在审核驳回状态下才显示驳回原因
			if (this.shifustatus === 4) {
				this.showRejectReason();
			}
		},

		// 显示驳回原因弹窗
		showRejectReason() {
			this.rejectReasonVisible = true;
		},

		// 隐藏驳回原因弹窗
		hideRejectReason() {
			this.rejectReasonVisible = false;
		},

		async getmyGrade() {
			try {
				// 先从缓存加载，确保立即显示
				const cachedLabelName = uni.getStorageSync('labelName');
				if (cachedLabelName && !this.labelName) {
					this.labelName = cachedLabelName;
					console.log('立即显示缓存的 labelName:', cachedLabelName);
				}

				// 然后异步获取最新数据
				const res = await this.$api.shifu.getGrade();
				console.log('getGrade response:', res);
				if (res && res.data && res.data.labelName) {
					this.labelName = res.data.labelName;
					// 缓存 labelName，下次可以立即显示
					uni.setStorageSync('labelName', res.data.labelName);
					console.log('更新并缓存 labelName:', res.data.labelName);

					// 如果数据有更新，强制刷新界面
					this.$forceUpdate();
				}
				return res;
			} catch (err) {
				console.error('获取师傅等级失败:', err);

				// 如果是401错误，清除登录状态
				if (err.status === 401 || (err.data && err.data.code === 401)) {
					console.log('师傅等级接口401错误，清除登录状态');
					this.handleInvalidSession();
					return null;
				}

				// 如果接口失败，保持使用缓存的值
				if (!this.labelName) {
					this.labelName = uni.getStorageSync('labelName') || '';
				}
				return null;
			}
		},
		getmylogin() {
			uni.login({
				provider: 'weixin',
				success: res => {
					if (res.code) {
						this.code = res.code;
						console.log('Initial wx.login code:', this.code);
					}
				}
			});
		},
		async getNowPosition() {
			try {
				// 使用统一的定位管理器，避免重复调用
				const locationData = await locationManager.getLocation({ forceUpdate: false, silent: true });

				if (locationData && locationData.regeocode) {
					// 更新 Vuex store - 使用正确的 action
					this.$store.dispatch('service/setRegeocode', {
						regeocode: locationData.regeocode,
						lat: locationData.lat,
						lng: locationData.lng
					});

					console.log("定位获取成功:", locationData);
				}
			} catch (error) {
				console.error("获取定位失败:", error);
				// 定位失败不影响页面功能
			}
		},
		getshifuinfo() {
			const userId = this.userInfo.userId || uni.getStorageSync('userId');
			if (!userId) {
				console.log('No userId, skipping getshifuinfo');
				return Promise.resolve();
			}
			return this.$api.shifu.getshifstutas({
				userId: userId
			}).then(res => {
				console.log('getshifstutas response:', res);
				this.shifustatus = (res.data !== undefined && res.data !== null) ? Number(res.data) : -1;
				if (res.data === -1) {
					const userinster = {
						userId: this.userInfo.userId || uni.getStorageSync('userId'),
						mobile: this.userInfo.phone || uni.getStorageSync('phone'),
						address: this.regeocode?.regeocode?.formatted_address || '',
						cityId: '',
						labelId: 25,
						lng: this.regeocode?.lng || uni.getStorageSync('lng') || 0,
						lat: this.regeocode?.lat || uni.getStorageSync('lat') || 0,
					};
					console.log('Registering master with:', userinster);
					return this.$api.shifu.masterEnter(userinster).then(res => {
						if (res.code === "200") {
							console.log('Master registration successful');
							return this.$api.shifu.getMaster();
						} else {
							throw new Error('Master registration failed');
						}
					}).then(masterRess => {
						if (!masterRess || typeof masterRess !== 'object') {
							throw new Error('获取师傅信息失败');
						}
						let masterRes = masterRess.data
						const userInfo = {
							phone: masterRes.mobile || this.userInfo.phone || uni.getStorageSync('phone') || '',
							avatarUrl: masterRes.avatarUrl || this.userInfo.avatarUrl || uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',
							nickName: masterRes.coachName || this.userInfo.nickName || uni.getStorageSync('nickName') || '微信用户',
							userId: masterRes.id || this.userInfo.userId || uni.getStorageSync('userId') || '',
							pid: masterRes.pid || this.userInfo.pid || uni.getStorageSync('pid') || ''
						};

						// 保存师傅详细信息
						this.shText = masterRes.shText || '';
						this.credit = masterRes.credit || 1;
						this.starRating = masterRes.starRating || '一星';

						uni.setStorageSync('shiInfo', JSON.stringify({
							mobile: userInfo.phone,
							avatarUrl: userInfo.avatarUrl,
							coachName: userInfo.nickName,
							userId: userInfo.userId,
							shifuId: masterRes.id || '',
							pid: userInfo.pid,
							status: this.shifustatus,
							messagePush: Number(masterRes.messagePush) || -1,
							shText: this.shText,
							credit: this.credit,
							starRating: this.starRating
						}));
						this.updateUserItem({
							key: 'userInfo',
							val: userInfo
						});
						this.saveUserInfoToStorage(userInfo);
						this.$nextTick(() => {
							this.$forceUpdate();
						});
					});
				} else {
					return this.$api.shifu.getMaster().then(masterRess => {
						if (!masterRess || typeof masterRess !== 'object') {
							throw new Error('获取师傅信息失败');
						}
						let masterRes = masterRess.data
						const userInfo = {
							phone: masterRes.mobile || this.userInfo.phone || uni.getStorageSync('phone') || '',
							avatarUrl: masterRes.avatarUrl || this.userInfo.avatarUrl || uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',
							nickName: masterRes.coachName || this.userInfo.nickName || uni.getStorageSync('nickName') || '微信用户',
							userId: masterRes.id || this.userInfo.userId || uni.getStorageSync('userId') || '',
							pid: masterRes.pid || this.userInfo.pid || uni.getStorageSync('pid') || ''
						};

						// 保存师傅详细信息
						this.shText = masterRes.shText || '';
						this.credit = masterRes.credit || 1;
						this.starRating = masterRes.starRating || '一星';

						uni.setStorageSync('shiInfo', JSON.stringify({
							mobile: userInfo.phone,
							avatarUrl: userInfo.avatarUrl,
							coachName: userInfo.nickName,
							userId: userInfo.userId,
							shifuId: masterRes.id || '',
							pid: userInfo.pid,
							status: this.shifustatus,
							messagePush: Number(masterRes.messagePush) || -1,
							shText: this.shText,
							credit: this.credit,
							starRating: this.starRating
						}));
						this.updateUserItem({
							key: 'userInfo',
							val: userInfo
						});
						this.saveUserInfoToStorage(userInfo);
						this.$nextTick(() => {
							this.$forceUpdate();
						});
					});
				}
			}).catch(err => {
				console.error('getshifuinfo error:', err);
				this.shifustatus = -1;
				const defaultUserInfo = {
					phone: this.userInfo.phone || uni.getStorageSync('phone') || '',
					avatarUrl: this.userInfo.avatarUrl || uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',
					nickName: this.userInfo.nickName || uni.getStorageSync('nickName') || '微信用户',
					userId: this.userInfo.userId || uni.getStorageSync('userId') || '',
					pid: this.userInfo.pid || uni.getStorageSync('pid') || ''
				};
				uni.setStorageSync('shiInfo', JSON.stringify({
					mobile: defaultUserInfo.phone,
					avatarUrl: defaultUserInfo.avatarUrl,
					coachName: defaultUserInfo.nickName,
					userId: defaultUserInfo.userId,
					shifuId: '',
					pid: defaultUserInfo.pid,
					status: -1,
					messagePush: -1
				}));
				this.updateUserItem({
					key: 'userInfo',
					val: defaultUserInfo
				});
				this.saveUserInfoToStorage(defaultUserInfo);
				this.$nextTick(() => {
					this.$forceUpdate();
				});
			});
		},
		debounceGetHighlight: debounce(function () {
			this.getHighlight();
		}, 300),
		getHighlight() {
			const userId = this.userInfo.userId || uni.getStorageSync('userId');
			if (!userId || !this.token || !this.isLoggedIn) {
				console.log('No userId, token, or not logged in, skipping getHighlight');
				return Promise.resolve();
			}
			// 确保只有在已登录状态下才显示loading
			this.isLoading = true;
			return this.$api.service.getHighlight({
				userId: userId,
				role: 1
			}).then(res => {
				console.log('getHighlight response:', res);
				const updatedOrderList = this.orderList.map((item, index) => ({
					...item,
					count: index === 0 ? (res && res.countOrder ? res.countOrder : 0) :
						index === 1 ? (res && res.shiFuBaoJia ? res.shiFuBaoJia : 0) :
							index === 2 ? (res && res.daiZhiFu ? res.daiZhiFu : 0) :
								index === 3 ? (res && res.daiFuWu ? res.daiFuWu : 0) :
									index === 4 ? (res && res.fuWuZhong ? res.fuWuZhong : 0) :
										index === 5 ? (res && res.yiWanCheng ? res.yiWanCheng : 0) : 0
				}));
				this.$set(this, 'orderList', updatedOrderList);
			}).finally(() => {
				this.isLoading = false;
			});
		},
		handleContact(e) {
			console.log(e.detail.path);
			console.log(e.detail.query);
		},
		// APP端客服电话拨打功能
		callCustomerService() {
			const phoneNumber = '13966580997';
			uni.makePhoneCall({
				phoneNumber: phoneNumber,
				success: () => {
					console.log('拨打客服电话成功');
				},
				fail: (err) => {
					console.error('拨打客服电话失败:', err);
					uni.showToast({
						title: '拨打失败，请稍后重试',
						icon: 'none'
					});
				}
			});
		},
		...mapMutations('user', ['updateUserItem']),
		showLoginPopup() {
			// 检查当前运行环境
			// #ifdef APP-PLUS
			console.log('APP端跳转到登录页面');
			uni.navigateTo({
				url: '/pages/login',
				fail: (err) => {
					console.error('跳转登录页面失败:', err);
					this.showToast('跳转失败，请重试');
				}
			});
			// #endif

			// #ifndef APP-PLUS
			console.log('非APP端显示登录弹窗');
			this.loginPopupVisible = true;
			// #endif

			// 运行时检查作为备用方案
			const systemInfo = uni.getSystemInfoSync();
			console.log('当前平台:', systemInfo.platform, systemInfo.app);
		},
		hideLoginPopup() {
			this.loginPopupVisible = false;
			this.agreedToTerms = false;
		},
		toggleAgreement() {
			this.agreedToTerms = !this.agreedToTerms;
		},
		navigateToAgreement(type) {
			let url = '../user/configuser';
			if (type === 'service') {
				url += '?type=service';
			} else if (type === 'privacy') {
				url += '?type=privacy';
			}
			uni.navigateTo({
				url: url
			});
		},
		initUserData() {
			console.log('=== 初始化用户数据 ===');
			console.log('当前token:', this.token);
			console.log('当前userInfo:', this.userInfo);

			if (this.token && (!this.userInfo.userId && !this.userInfo.phone)) {
				console.log('有token但Vuex中没有用户信息，从本地存储恢复');

				const userInfo = {
					phone: uni.getStorageSync('phone') || '',
					avatarUrl: uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',
					nickName: uni.getStorageSync('nickName') || '微信用户',
					userId: uni.getStorageSync('userId') || '',
					pid: uni.getStorageSync('pid') || '',
					unionid: uni.getStorageSync('unionid') || ''
				};

				console.log('从本地存储获取的用户信息:', userInfo);

				// 只要有userId就保存用户信息（微信登录可能没有手机号）
				if (userInfo.userId) {
					console.log('保存用户信息到Vuex');
					this.updateUserItem({
						key: 'userInfo',
						val: userInfo
					});
					this.saveUserInfoToStorage(userInfo);
					console.log('用户数据初始化成功');
					this.$nextTick(() => {
						this.$forceUpdate();
					});
				} else {
					console.log('本地存储中没有userId，尝试从服务器获取用户信息');
					// 不立即清除登录状态，而是尝试从服务器获取用户信息
					this.fetchUserInfoFromServer();
				}
			} else if (this.token && (this.userInfo.userId || this.userInfo.phone)) {
				console.log('token和用户信息都存在，无需初始化');
			} else {
				console.log('没有token，跳过初始化');
			}
		},

		// 从服务器获取用户信息
		async fetchUserInfoFromServer() {
			console.log('=== 从服务器获取用户信息 ===');
			try {
				// 尝试获取用户信息
				const response = await this.$api.user.userInfo();
				if (response && response.data) {
					const userData = response.data;
					console.log('从服务器获取的用户信息:', userData);

					const userInfoFormatted = {
						phone: userData.phone || '',
						avatarUrl: userData.avatarUrl || '/static/mine/default_user.png',
						nickName: userData.nickName || '微信用户',
						userId: userData.id || userData.userId || '',
						createTime: userData.createTime || '',
						pid: userData.pid || '',
						inviteCode: userData.inviteCode || '',
						unionid: userData.unionid || ''
					};

					if (userInfoFormatted.userId) {
						console.log('服务器返回了有效的用户信息，保存到本地');
						this.updateUserItem({
							key: 'userInfo',
							val: userInfoFormatted
						});
						this.saveUserInfoToStorage(userInfoFormatted);
						this.$nextTick(() => {
							this.$forceUpdate();
						});
					} else {
						console.log('服务器返回的用户信息无效，清除登录状态');
						this.handleInvalidSession();
					}
				} else {
					console.log('服务器未返回用户信息，清除登录状态');
					this.handleInvalidSession();
				}
			} catch (error) {
				console.error('从服务器获取用户信息失败:', error);
				// 如果是401错误（未授权），清除登录状态
				if (error.status === 401 || (error.data && error.data.code === 401)) {
					console.log('token已失效，清除登录状态');
					this.handleInvalidSession();
				} else {
					// 其他错误，暂时不清除登录状态，可能是网络问题
					console.log('网络错误，暂时保持登录状态');
				}
			}
		},

		// 加载用户相关数据的方法
		loadUserRelatedData() {
			console.log('=== 开始加载用户相关数据 ===');
			if (!this.isLoggedIn || !this.token) {
				console.log('用户未登录，跳过数据加载');
				return;
			}

			// 并行加载多个数据，包括师傅等级信息
			const promises = [
				this.getHighlight() || Promise.resolve(),
				this.fetchShifuInfo() || Promise.resolve(),
				this.getmyGrade() || Promise.resolve()
			];

			Promise.all(promises.map(promise =>
				Promise.resolve(promise).catch(err => {
					console.error('加载数据失败:', err);
					return null;
				})
			)).then(() => {
				console.log('用户相关数据加载完成');
				this.$nextTick(() => {
					this.$forceUpdate();
				});
			}).catch(err => {
				console.error('加载用户数据时发生错误:', err);
			});
		},

		// 获取师傅信息的方法（优化版本）
		fetchShifuInfo() {
			console.log('=== 开始获取师傅信息 ===');
			const userId = this.userInfo.userId || uni.getStorageSync('userId');
			if (!userId) {
				console.log('没有userId，跳过师傅信息获取');
				return Promise.resolve();
			}

			// 先检查本地缓存，如果有最近的数据就直接使用
			const cachedShiInfo = uni.getStorageSync('shiInfo');
			const cacheTimestamp = uni.getStorageSync('shiInfoTimestamp');
			const now = Date.now();
			const cacheExpiry = 5 * 60 * 1000; // 5分钟缓存

			if (cachedShiInfo && cacheTimestamp && (now - cacheTimestamp < cacheExpiry)) {
				try {
					const parsedShiInfo = JSON.parse(cachedShiInfo);
					this.shifustatus = parsedShiInfo.status || -1;
					console.log('使用缓存的师傅信息:', parsedShiInfo.status);
					return Promise.resolve();
				} catch (error) {
					console.error('解析缓存的师傅信息失败:', error);
				}
			}

			// 缓存过期或不存在，重新获取
			return this.getshifuinfo().then(() => {
				// 更新缓存时间戳
				uni.setStorageSync('shiInfoTimestamp', now);
				console.log('师傅信息获取完成并更新缓存');
			}).catch(err => {
				console.error('获取师傅信息失败:', err);
				// 即使失败也不影响页面显示
			});
		},

		// 立即获取师傅信息的方法（用于解决未知状态问题）
		fetchShifuInfoImmediately() {
			console.log('=== 立即获取师傅信息 ===');
			const userId = this.userInfo.userId || uni.getStorageSync('userId');
			if (!userId) {
				console.log('没有userId，无法立即获取师傅信息');
				return Promise.resolve();
			}

			// 强制重新获取，不使用缓存
			return this.getshifuinfo().then(() => {
				// 更新缓存时间戳
				uni.setStorageSync('shiInfoTimestamp', Date.now());
				console.log('立即获取师傅信息完成，状态:', this.shifustatus);
				// 强制更新界面
				this.$forceUpdate();
			}).catch(err => {
				console.error('立即获取师傅信息失败:', err);
				// 设置默认状态
				this.shifustatus = -1;
				this.$forceUpdate();
			});
		},
		// 添加缺失的 handleNavigate 方法
		handleNavigate(url) {
			if (!url) return;
			console.log('handleNavigate called with url:', url);

			// 检查是否需要登录
			const requiresLogin = [
				'/shifu/skills',
				'/shifu/Professiona',
				'/shifu/coreWallet',
				'/user/promotion',
				'/shifu/income',        // orderList3 中的服务收入
				'/shifu/master_bao_list', // orderList3 中的报价列表
				'/shifu/master_my_order',
				'/shifu/Margin',        // orderList3 中的保证金
				'/shifu/shifuGrade',    // orderList3 中的师傅等级
				'/shifu/Settle',        // toolList2 中的师傅入驻
				'/shifu/master_Info'    // toolList2 中的编辑师傅资料
			];

			if (requiresLogin.some(path => url.startsWith(path)) && !this.isLoggedIn) {
				return this.showLoginPopup();
			}

			uni.navigateTo({
				url,
				fail: (err) => {
					console.error('页面跳转失败:', err);
					this.showToast('页面跳转失败，请重试');
				}
			});
		},

		navigateTo(url) {
			if (!url) return;
			const requiresLogin = [
				'/shifu/master_my_order', // orderList 中的所有订单页面
				'../user/userProfile',    // 用户资料页面
				// '../user/coupon', // These were commented out in the original code
				// '../user/repair_record',
				// '../user/order_list',
				// '../user/address',
				// '../user/Settle',
				// '../user/agent_apply',
				// '../user/promotion',
				// '../user/bankCard',
				// '../shifu/Settle',
				// '../shifu/Receiving',
				// '../shifu/mine'
			];
			if (requiresLogin.some(path => url.startsWith(path)) && !this.isLoggedIn) {
				return this.showLoginPopup();
			}
			uni.navigateTo({
				url
			});
		},

		// 添加缺失的 showToast 方法
		showToast(message, icon = 'none') {
			uni.showToast({
				title: message,
				icon: icon,
				duration: 2000
			});
		},

		// 添加缺失的手机号验证方法
		validatePhone(phone) {
			const phoneRegex = /^1[3-9]\d{9}$/;
			return phoneRegex.test(phone);
		},

		// 添加微信登录获取手机号方法
		onGetPhoneNumber(e) {
			console.log('获取手机号:', e);
			if (!this.agreedToTerms) {
				this.showToast('请先同意服务协议和隐私政策');
				return;
			}

			if (e.detail.errMsg === 'getPhoneNumber:ok') {
				this.isLoading = true;
				// 处理微信登录逻辑
				this.handleWechatLogin(e.detail);
			} else {
				this.showToast('获取手机号失败，请重试');
			}
		},

		// 处理微信登录
		async handleWechatLogin(phoneDetail) {
			try {
				// 这里应该调用你的登录API
				const loginData = {
					code: this.code,
					encryptedData: phoneDetail.encryptedData,
					iv: phoneDetail.iv,
					inviteCode: this.inviteCode || ''
				};

				const response = await this.$api.user.wxLogin(loginData);

				if (response && response.code === '200') {
					// 登录成功，保存用户信息
					const userInfo = response.data;
					this.updateUserItem({
						key: 'autograph',
						val: userInfo.token || ''
					});
					this.updateUserItem({
						key: 'userInfo',
						val: {
							phone: userInfo.phone || '',
							avatarUrl: userInfo.avatarUrl || '/static/mine/default_user.png',
							nickName: userInfo.nickName || '微信用户',
							userId: userInfo.userId || userInfo.id || '',
							pid: userInfo.pid || ''
						}
					});

					this.hideLoginPopup();
					this.showToast('登录成功', 'success');

					// 登录成功后加载用户相关数据
					this.loadUserRelatedData();
				} else {
					throw new Error(response.msg || '登录失败');
				}
			} catch (error) {
				console.error('微信登录失败:', error);
				this.showToast(error.message || '登录失败，请重试');
			} finally {
				this.isLoading = false;
			}
		},

		// 显示绑定手机号弹窗
		showBindPhonePopup() {
			this.bindPhonePopupVisible = true;
		},

		// 隐藏绑定手机号弹窗
		hideBindPhonePopup() {
			this.bindPhonePopupVisible = false;
			this.bindPhoneForm = {
				phone: '',
				code: ''
			};
		},

		// 发送绑定手机号验证码
		async sendBindPhoneSmsCode() {
			if (!this.validatePhone(this.bindPhoneForm.phone)) {
				this.showToast('请输入正确的手机号');
				return;
			}

			if (this.bindPhoneSmsCountdown > 0) {
				return;
			}

			try {
				await this.$api.user.sendSmsCode({
					phone: this.bindPhoneForm.phone,
					type: 'bind'
				});

				this.showToast('验证码已发送', 'success');
				this.startBindPhoneSmsCountdown();
			} catch (error) {
				console.error('发送验证码失败:', error);
				this.showToast(error.message || '发送验证码失败');
			}
		},

		// 开始验证码倒计时
		startBindPhoneSmsCountdown() {
			this.bindPhoneSmsCountdown = 60;
			this.bindPhoneSmsTimer = setInterval(() => {
				this.bindPhoneSmsCountdown--;
				if (this.bindPhoneSmsCountdown <= 0) {
					clearInterval(this.bindPhoneSmsTimer);
					this.bindPhoneSmsTimer = null;
				}
			}, 1000);
		},


		saveUserInfoToStorage(userInfo) {
			uni.setStorageSync('phone', userInfo.phone || '');
			uni.setStorageSync('avatarUrl', userInfo.avatarUrl || '');
			uni.setStorageSync('nickName', userInfo.nickName || '');
			uni.setStorageSync('userId', userInfo.userId || '');
			uni.setStorageSync('pid', userInfo.pid || '');
			if (userInfo.unionid) {
				uni.setStorageSync('unionid', userInfo.unionid);
			}
		},
		fetchUserInfo() {
			if (this.isLoading || !this.token) {
				console.log('Skipping fetchUserInfo: no token or already loading');
				return Promise.resolve();
			}
			this.isLoading = true;
			return this.$api.user.userInfo()
				.then(responses => {
					let response = responses.data
					if (!response || typeof response !== 'object') {
						throw new Error('获取用户信息失败: 响应数据无效');
					}
					const userInfo = {
						phone: response.data.phone || '',
						avatarUrl: response.data.avatarUrl || '/static/mine/default_user.png',
						nickName: response.data.nickName || '微信用户',
						userId: response.data.id || '',
						createTime: response.data.createTime || '',
						pid: response.data.pid || '',
						inviteCode: response.data.inviteCode || ''
					};
					this.updateUserItem({
						key: 'userInfo',
						val: userInfo
					});
					console.log(userInfo)
					this.saveUserInfoToStorage(userInfo);
				})
				.catch(error => {
					console.error('获取用户信息失败:', error);
					if (error.message && error.message.includes('响应数据无效')) {
						this.handleInvalidSession();
					} else {
						if (this.token) {
							// this.showToast('获取用户信息失败，请稍后重试');
						}
					}
				})
				.finally(() => {
					this.isLoading = false;
				});
		},
		handleInvalidSession() {
			console.log('=== 清除登录状态 ===');

			// 清除所有用户相关的本地存储数据
			const keysToRemove = [
				'token', 'phone', 'avatarUrl', 'nickName', 'userId', 'pid',
				'shiInfo', 'shiInfoTimestamp', 'labelName', 'unionid', 'appOpenid',
				'receivedInviteCode', 'userInfo'
			];

			keysToRemove.forEach(key => {
				uni.removeStorageSync(key);
				console.log(`已清除本地存储: ${key}`);
			});

			// 清除 Vuex 中的用户数据
			this.updateUserItem({
				key: 'userInfo',
				val: {}
			});
			this.updateUserItem({
				key: 'autograph',
				val: ''
			});

			// 重置页面状态
			this.isLoading = false;
			this.shifustatus = -1;
			this.labelName = '';

			// 重置订单列表计数
			this.$set(this, 'orderList', this.orderList.map(item => ({
				...item,
				count: 0
			})));

			console.log('登录状态清除完成');

			// 强制更新界面
			this.$nextTick(() => {
				this.$forceUpdate();
			});
		},
		// 绑定手机号相关方法
		showBindPhonePopup() {
			this.bindPhonePopupVisible = true;
		},
		hideBindPhonePopup() {
			this.bindPhonePopupVisible = false;
			this.bindPhoneForm = { phone: '', code: '' };
			if (this.bindPhoneSmsTimer) {
				clearInterval(this.bindPhoneSmsTimer);
				this.bindPhoneSmsTimer = null;
				this.bindPhoneSmsCountdown = 0;
			}
		},

		// 验证手机号
		validatePhone(phone) {
			const phoneReg = /^1[3-9]\d{9}$/;
			return phoneReg.test(phone);
		},

		// 发送绑定手机号验证码
		async sendBindPhoneSmsCode() {
			if (this.bindPhoneSmsCountdown > 0) return;

			const phone = this.bindPhoneForm.phone;
			if (!this.validatePhone(phone)) {
				return this.showToast('请输入正确的手机号');
			}

			try {
				// 调用发送验证码接口
				const response = await this.$api.base.sendSmsCode({ phone });

				if (response.code === '200') {
					this.showToast('验证码发送成功', 'success');
					this.startBindPhoneCountdown();
				} else {
					this.showToast(response.msg || '验证码发送失败，请重试');
				}
			} catch (error) {
				console.error('发送验证码失败:', error);
				this.showToast('验证码发送失败，请重试');
			}
		},

		// 开始绑定手机号倒计时
		startBindPhoneCountdown() {
			this.bindPhoneSmsCountdown = 60;
			this.bindPhoneSmsTimer = setInterval(() => {
				this.bindPhoneSmsCountdown--;
				if (this.bindPhoneSmsCountdown <= 0) {
					clearInterval(this.bindPhoneSmsTimer);
					this.bindPhoneSmsTimer = null;
				}
			}, 1000);
		},

		// 处理绑定手机号
		async handleBindPhone() {
			if (!this.canBindPhone || this.isBindingPhone) return;

			const { phone, code } = this.bindPhoneForm;

			if (!this.validatePhone(phone)) {
				return this.showToast('请输入正确的手机号');
			}

			if (!code) {
				return this.showToast('请输入验证码');
			}

			this.isBindingPhone = true;
			uni.showLoading({ title: '绑定中...' });

			try {
				// 获取unionid
				const unionid = uni.getStorageSync('unionid');
				if (!unionid) {
					throw new Error('缺少微信用户标识，请重新登录');
				}
				const registerID = uni.getStorageSync("registerID")

				// 调用绑定接口
				const params = {
					phone: phone,
					shortCode: code,
					unionid: unionid,
					platform: 1, // 师傅端
					registrationId: registerID || 'xxx' // 极光推送id
				};

				console.log('绑定手机号参数:', params);

				const response = await this.$api.user.register(params);
				console.log('绑定手机号响应:', response);

				if (response.code === '200') {
					this.showToast('绑定成功', 'success');

					// 更新用户信息
					const updatedUserInfo = {
						...this.userInfo,
						phone: phone
					};

					this.updateUserItem({
						key: 'userInfo',
						val: updatedUserInfo
					});

					// 更新本地存储
					uni.setStorageSync('phone', phone);

					// 更新师傅信息存储
					const shiInfo = uni.getStorageSync('shiInfo') ? JSON.parse(uni.getStorageSync('shiInfo')) : {};
					shiInfo.mobile = phone;
					uni.setStorageSync('shiInfo', JSON.stringify(shiInfo));

					// 关闭弹窗
					this.hideBindPhonePopup();

					// 刷新用户信息
					this.fetchUserInfo();
				} else {
					throw new Error(response.msg || '绑定失败，请重试');
				}
			} catch (error) {
				console.error('绑定手机号失败:', error);
				this.showToast(error.message || '绑定失败，请重试');
			} finally {
				this.isBindingPhone = false;
				uni.hideLoading();
			}
		},

		onGetPhoneNumber(e) {
			// #ifdef APP-PLUS
			uni.navigateTo({
				url: '/pages/login'
			});
			return;
			// #endif
			if (e.detail.errMsg !== 'getPhoneNumber:ok') {
				return this.showToast('授权失败，请重试');
			}
			this.getmylogin();
			this.isLoading = true;
			uni.showLoading({
				mask: true,
				title: '登录中...'
			});
			const {
				encryptedData,
				iv
			} = e.detail;
			uni.checkSession({
				success: () => {
					this.loginWithWeixin({
						code: this.code,
						encryptedData,
						iv,
						platform: 1,
						pid: this.inviteCode
					});
				},
				fail: () => {
					uni.login({
						provider: 'weixin',
						success: res => {
							if (res.code) {
								this.code = res.code;
								console.log('Refreshed wx.login code:', this.code);
								this.loginWithWeixin({
									code: this.code,
									encryptedData,
									iv,
									platform: 1,
									pid: this.inviteCode
								});
							} else {
								this.isLoading = false;
								uni.hideLoading();
								this.showToast('获取登录凭证失败');
							}
						},
						fail: () => {
							this.isLoading = false;
							uni.hideLoading();
							this.showToast('微信登录失败，请重试');
						}
					});
				}
			});
		},
		async loginWithWeixin(params) {
			try {
				const response = await this.$api.user.loginuserInfo({
					code: params.code,
					encryptedData: params.encryptedData,
					iv: params.iv,
					platform: 1,
					pid: this.inviteCode
				});

				console.log('登录响应:', response)
				if (!response || !response.data.token) {
					throw new Error('请重新登录');
				}

				// 确保token被正确保存到所有地方
				uni.setStorageSync('token', response.data.token);

				// 使用 await 确保 Vuex 更新完成
				await this.$nextTick();
				this.updateUserItem({
					key: 'autograph',
					val: response.data.token
				});

				// 再次等待确保状态更新完成
				await this.$nextTick();

				console.log('Token saved:', response.data.token);
				console.log('Token from Vuex:', this.$store.state.user.autograph);
				console.log('Token from storage:', uni.getStorageSync('token'));

				// 返回 userInfo 请求，确保 token 已设置
				const userInfoResponse = await this.$api.user.userInfo();

				let userInfoRes = userInfoResponse.data;
				if (!userInfoRes || typeof userInfoRes !== 'object') {
					console.error('获取用户信息失败 - 响应数据:', userInfoRes);
					throw new Error('获取用户信息失败');
				}
				console.log('用户信息获取成功:', userInfoRes)

				const initialUserInfo = {
					phone: userInfoRes.phone || '',
					avatarUrl: userInfoRes.avatarUrl || '/static/mine/default_user.png',
					nickName: userInfoRes.nickName || '微信用户',
					userId: userInfoRes.id || '',
					pid: userInfoRes.pid || ''
				};
				this.updateUserItem({
					key: 'userInfo',
					val: initialUserInfo
				});
				this.saveUserInfoToStorage(initialUserInfo);

				// 直接获取师傅信息
				const masterRes = await this.$api.shifu.getMaster();

				console.log('getMaster响应:', masterRes);
				if (!masterRes || typeof masterRes !== 'object') {
					throw new Error('获取师傅信息失败');
				}
				let masterData = masterRes.data;

				// 如果getMaster返回了状态信息，优先使用
				if (masterData && masterData.status !== undefined) {
					this.shifustatus = Number(masterData.status);
					console.log('从getMaster获取的状态:', this.shifustatus);
				} else {
					// 如果getMaster没有返回状态，使用之前获取的状态
					console.log('getMaster未返回状态，使用当前状态:', this.shifustatus);
				}

				const userInfo = {
					phone: masterData.mobile || this.userInfo.phone || uni.getStorageSync('phone') || '',
					avatarUrl: masterData.avatarUrl || this.userInfo.avatarUrl || uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',
					nickName: masterData.coachName || this.userInfo.nickName || uni.getStorageSync('nickName') || '微信用户',
					userId: masterData.userId || this.userInfo.userId || uni.getStorageSync('userId') || '',
					shifuId: masterData.id || '',
					pid: masterData.pid || this.userInfo.pid || uni.getStorageSync('pid') || ''
				};

				// 保存师傅详细信息
				this.shText = masterData.shText || '';
				this.credit = masterData.credit || 1;
				this.starRating = masterData.starRating || '一星';

				// 保存师傅信息到本地存储
				const shiInfoData = {
					mobile: userInfo.phone,
					avatarUrl: userInfo.avatarUrl,
					coachName: userInfo.nickName,
					shifuId: userInfo.shifuId,
					userId: userInfo.userId,
					pid: userInfo.pid,
					status: this.shifustatus,
					messagePush: Number(masterData.messagePush) || -1,
					shText: this.shText,
					credit: this.credit,
					starRating: this.starRating
				};

				uni.setStorageSync('shiInfo', JSON.stringify(shiInfoData));
				// 立即更新缓存时间戳，确保状态能立即显示
				uni.setStorageSync('shiInfoTimestamp', Date.now());

				console.log('登录完成，保存师傅信息:', shiInfoData);
				console.log('最终师傅状态:', this.shifustatus);

				this.updateUserItem({
					key: 'userInfo',
					val: userInfo
				});
				this.saveUserInfoToStorage(userInfo);

				// 异步获取师傅等级信息，确保 labelName 能正确显示
				// 由于 getMaster API 不包含 labelName，需要单独调用 getGrade
				this.getmyGrade();

				// 立即触发界面更新
				this.$forceUpdate();

				const modalShownKey = `certificationModalShown_${userInfo.userId}_${this.shifustatus}`;
				const hasShownModal = uni.getStorageSync(modalShownKey);
				if (!hasShownModal && (this.shifustatus === -1 || this.shifustatus === 4) && this.isLoggedIn) {
					this.showCertificationPopup();
					uni.setStorageSync(modalShownKey, 'true');
				}

				this.showToast('登录成功', 'success');
				this.hideLoginPopup();
				this.debounceGetHighlight();

				// 强制更新界面，确保状态文本立即显示
				this.$nextTick(() => {
					// 确保状态文本能立即更新
					this.$forceUpdate();
					// 再次确认状态已正确设置
					console.log('登录完成后的最终状态检查:', {
						shifustatus: this.shifustatus,
						statusText: this.statusText,
						labelName: this.labelName,
						cachedShiInfo: uni.getStorageSync('shiInfo'),
						isLoggedIn: this.isLoggedIn,
						displayUserInfo: this.displayUserInfo
					});

					// 额外的强制更新，确保界面响应
					setTimeout(() => {
						this.$forceUpdate();
						console.log('延迟强制更新完成');
					}, 100);
				});

			} catch (error) {
				console.error('Login error:', error);
				this.showToast(error.message || '登录失败，请稍后重试');
				this.handleInvalidSession();
			} finally {
				this.isLoading = false;
				uni.hideLoading();
			}
		},



		showToast(title, icon = 'none') {
			uni.showToast({
				title,
				icon,
				duration: 2000
			});
		},
		async fetchShifuInfo() {
			try {
				this.isLoading = true;
				const shiInfoResponses = await this.$api.shifu.getMaster();
				let shiInfoResponse = shiInfoResponses.data
				console.log('fetchShifuInfo response:', shiInfoResponse);
				if (!shiInfoResponses || typeof shiInfoResponses !== 'object') {
					throw new Error('获取师傅状态失败: 响应数据无效');
				}
				this.shifustatus = (shiInfoResponse.status !== undefined && shiInfoResponse.status !== null) ? Number(shiInfoResponse.status) : -1;

				// 保存师傅详细信息
				this.shText = shiInfoResponse.shText || '';
				this.credit = shiInfoResponse.credit || 1;
				this.starRating = shiInfoResponse.starRating || '一星';

				const userInfo = {
					phone: shiInfoResponse.mobile || this.userInfo.phone || uni.getStorageSync('phone') || '',
					avatarUrl: shiInfoResponse.avatarUrl || this.userInfo.avatarUrl || uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',
					nickName: shiInfoResponse.coachName || this.userInfo.nickName || uni.getStorageSync('nickName') || '微信用户',
					shifuId: shiInfoResponse.id || this.userInfo.userId || uni.getStorageSync('userId') || '',
					userId: shiInfoResponse.userId || this.userInfo.userId || uni.getStorageSync('userId') || '',
					pid: shiInfoResponse.pid || this.userInfo.pid || uni.getStorageSync('pid') || ''
				};
				uni.setStorageSync('shiInfo', JSON.stringify({
					mobile: userInfo.phone,
					avatarUrl: userInfo.avatarUrl,
					coachName: userInfo.nickName,
					userId: userInfo.userId,
					shifuId: userInfo.shifuId,
					pid: userInfo.pid,
					status: this.shifustatus,
					messagePush: Number(shiInfoResponse.messagePush) || -1,
					shText: this.shText,
					credit: this.credit,
					starRating: this.starRating
				}));
				this.updateUserItem({
					key: 'userInfo',
					val: userInfo
				});
				const modalShownKey = `certificationModalShown_${userInfo.userId}_${this.shifustatus}`;
				const hasShownModal = uni.getStorageSync(modalShownKey);
				if (!hasShownModal && (this.shifustatus === -1 || this.shifustatus === 4) && this.isLoggedIn) {
					this.showCertificationPopup();
					uni.setStorageSync(modalShownKey, 'true');
				}
				this.$nextTick(() => {
					this.$forceUpdate();
				});
			} catch (error) {
				console.error('fetchShifuInfo error:', error);
				this.shifustatus = -1;
				const defaultUserInfo = {
					phone: this.userInfo.phone || uni.getStorageSync('phone') || '',
					avatarUrl: this.userInfo.avatarUrl || uni.getStorageSync('avatarUrl') || '/static/mine/default_user.png',
					nickName: this.userInfo.nickName || uni.getStorageSync('nickName') || '微信用户',
					userId: this.userInfo.userId || uni.getStorageSync('userId') || '',
					pid: this.userInfo.pid || uni.getStorageSync('pid') || ''
				};
				uni.setStorageSync('shiInfo', JSON.stringify({
					mobile: defaultUserInfo.phone,
					avatarUrl: defaultUserInfo.avatarUrl,
					coachName: defaultUserInfo.nickName,
					userId: defaultUserInfo.userId,
					shifuId: '', // 默认为空字符串
					pid: defaultUserInfo.pid,
					status: -1,
					messagePush: -1
				}));
				this.updateUserItem({
					key: 'userInfo',
					val: defaultUserInfo
				});
				const modalShownKey = `certificationModalShown_${defaultUserInfo.userId}_${defaultUserInfo.status}`;
				const hasShownModal = uni.getStorageSync(modalShownKey);
				if (!hasShownModal && defaultUserInfo.status === -1 && this.isLoggedIn) {
					this.showCertificationPopup();
					uni.setStorageSync(modalShownKey, 'true');
				}
				this.$nextTick(() => {
					this.$forceUpdate();
				});
			} finally {
				this.isLoading = false;
			}
		},
		getshifustatus() {
			const shiInfo = uni.getStorageSync('shiInfo') ? JSON.parse(uni.getStorageSync('shiInfo')) : {};
			this.shifustatus = shiInfo.status;
			console.log('getshifustatus:', this.shifustatus);
		},
		showCertificationPopup() {
			console.log('showCertificationPopup called, current shifustatus:', this.shifustatus);
			if (this.shifustatus === -1 || this.shifustatus === 4) {
				uni.showModal({
					title: '提示',
					content: this.shifustatus === -1 ? '您尚未成为师傅，是否前往认证？' : '您的师傅认证被驳回，是否重新认证？',
					confirmText: '去认证',
					cancelText: '再想想',
					cancelable: true,
					success: (res) => {
						if (res.confirm) {
							const targetUrl = '/shifu/Settle';
							uni.navigateTo({
								url: targetUrl,
								fail(err) {
									console.error('Navigation to certification failed:', err);
									uni.showToast({
										title: '跳转认证页面失败',
										icon: 'none'
									});
								}
							});
						}
					},
					fail: (err) => {
						console.error('Modal failed:', err);
					}
				});
			}
		},
		handleNavigate(url) {
			const directNavigatePaths = ['/shifu/Settle','/user/promotion', '/shifu/master_Info'];

			if (directNavigatePaths.includes(url)) {
				this.navigateTo(url);
				return;
			}

			// 先检查是否已登录
			if (!this.isLoggedIn) {
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				});
				this.showLoginPopup();
				return;
			}

			if (this.shifustatus === -1 || this.shifustatus === 4) {
				uni.showToast({
					title: '你还不是师傅',
					icon: 'none'
				});
				this.showCertificationPopup();
			} else if (this.shifustatus === 1) {
				uni.showToast({
					title: '师傅状态在审核中',
					icon: 'none'
				});
			} else if (this.shifustatus === 2) {
				this.navigateTo(url);
			} else {
				this.navigateTo(url); // Fallback to navigate if status is unexpected.
			}
		},
		handleCallKf() {
			// 先检查是否已登录
			if (!this.isLoggedIn) {
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				});
				this.showLoginPopup();
				return;
			}

			if (this.shifustatus === -1 || this.shifustatus === 4) {
				uni.showToast({
					title: '你还不是师傅',
					icon: 'none'
				});
				this.showCertificationPopup();
			} else if (this.shifustatus === 1) {
				uni.showToast({
					title: '师傅状态在审核中',
					icon: 'none'
				});
			} else if (this.shifustatus === 2) {
				this.callkf();
			}
		},
		callkf() {
			uni.showToast({
				title: '联系客服功能待实现',
				icon: 'none'
			});
		},

		showToast(title, icon = 'none') {
			uni.showToast({
				title,
				icon,
				duration: 2000
			});
		}
	}
};
</script>

<style lang="scss">
/* Login Popup Styles */
.login-popup-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 2000;
	display: flex;
	align-items: flex-end;
	justify-content: center;
}

.login-popup {
	background-color: #fff;
	width: 100%;
	border-radius: 40rpx 40rpx 0 0;
	position: relative;
	max-height: 60vh;
	padding-bottom: 10rpx;
	animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
	from {
		transform: translateY(100%);
	}

	to {
		transform: translateY(0);
	}
}

.close-btn {
	position: absolute;
	top: 30rpx;
	right: 30rpx;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #999;
	font-size: 40rpx;
	z-index: 10;
}

.popup-content {
	padding: 80rpx 60rpx 40rpx;
	text-align: center;
}

.welcome-title {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.welcome-subtitle {
	font-size: 32rpx;
	color: #666;
	margin-bottom: 80rpx;
}

.agreement-section {
	margin-bottom: 60rpx;
	display: flex;
	justify-content: center;
}

.checkbox-container {
	display: flex;
	align-items: flex-start;
	text-align: left;
	max-width: 560rpx;
}

.checkbox {
	width: 36rpx;
	height: 36rpx;
	border: 2rpx solid #ddd;
	border-radius: 6rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
	margin-top: 4rpx;
	flex-shrink: 0;
	background-color: #fff;
	transition: all 0.2s;

	&.checked {
		background-color: #00C853;
		border-color: #00C853;
		color: #fff;
	}

	.iconfont {
		font-size: 24rpx;
	}
}

.agreement-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;

	.link {
		color: #00C853;
	}
}

.phone-login-btn {
	width: 100%;
	height: 100rpx;
	background: linear-gradient(135deg, #00C853, #4CAF50);
	border: none;
	border-radius: 50rpx;
	color: #fff;
	font-size: 36rpx;
	font-weight: bold;
	margin-bottom: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 20rpx rgba(0, 200, 83, 0.3);
	transition: all 0.2s;

	&:active:not(.disabled) {
		transform: translateY(2rpx);
		box-shadow: 0 4rpx 12rpx rgba(0, 200, 83, 0.3);
	}

	&.disabled {
		background: #ccc;
		box-shadow: none;
		opacity: 0.6;
	}

	&::after {
		border: none;
	}
}

.bind-phone-container {
	margin-top: 10rpx;
}

.bind-phone-btn {
	background: none;
	border: 2rpx solid rgba(255, 255, 255, 0.8);
	border-radius: 32rpx;
	color: #fff;
	font-size: 28rpx;
	line-height: 1.5;
	padding: 8rpx 24rpx;
	transition: all 0.2s;

	&:active:not([disabled]) {
		background: rgba(255, 255, 255, 0.1);
		transform: scale(0.98);
	}

	&[disabled] {
		opacity: 0.6;
	}

	&::after {
		border: none;
	}
}

.input-group {
	margin-bottom: 40rpx;

	.input-item {
		display: flex;
		align-items: center;
		background: #f8fafc;
		border: 2rpx solid #e2e8f0;
		border-radius: 16rpx;
		margin-bottom: 24rpx;
		min-height: 88rpx;
		padding: 0;
		transition: all 0.3s ease;

		&:focus-within {
			border-color: #3b82f6;
			box-shadow: 0 0 0 6rpx rgba(59, 130, 246, 0.08);
		}

		.input-icon {
			width: 50rpx;
			height: 50rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-left: 24rpx;
			background: rgba(59, 130, 246, 0.08);
			border-radius: 50%;
			flex-shrink: 0;
		}

		.input-field {
			flex: 1;
			margin-left: 24rpx;
			margin-right: 16rpx;
			font-size: 32rpx;
			color: #1e293b;
			background: transparent;
			border: none;
			outline: none;

			&::placeholder {
				color: #94a3b8;
			}
		}

		.sms-btn {
			background: linear-gradient(135deg, #3b82f6, #1d4ed8);
			color: #fff;
			padding: 16rpx 24rpx;
			border-radius: 12rpx;
			font-size: 24rpx;
			font-weight: 500;
			margin-right: 16rpx;
			transition: all 0.3s ease;
			box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.2);
			flex-shrink: 0;

			&.disabled {
				background: #cbd5e1;
				color: #64748b;
				box-shadow: none;
			}

			&:not(.disabled):active {
				transform: scale(0.95);
			}
		}
	}
}

.alternative-login {
	display: flex;
	align-items: center;
	margin-bottom: 40rpx;

	.divider-line {
		flex: 1;
		height: 1rpx;
		background-color: #eee;
	}

	.divider-text {
		font-size: 26rpx;
		color: #999;
		margin: 0 30rpx;
	}
}

.sms-login-btn {
	width: 100%;
	height: 88rpx;
	background-color: #fff;
	border: 2rpx solid #ddd;
	border-radius: 44rpx;
	color: #666;
	font-size: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s;

	&:active {
		background-color: #f8f8f8;
		border-color: #00C853;
	}

	&::after {
		border: none;
	}

	.iconfont {
		margin-right: 16rpx;
		font-size: 36rpx;
	}
}

/* Floating Contact Button Styles */
.floating-contact {
	position: fixed;
	bottom: 150rpx;
	right: 30rpx;
	z-index: 1000;
	background-color: #fff;
	border-radius: 50rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
	padding: 10rpx 20rpx;
	display: flex;
	align-items: center;
}

.contact-container {
	display: flex;
	align-items: center;
}

.contact-btn {
	background: none;
	border: none;
	color: #576b95;
	font-size: 30rpx;
	line-height: 1.5;
	padding: 10rpx 20rpx;
	display: flex;
	align-items: center;
}

.contact-btn:active {
	background-color: #ededee;
}

/* Existing Styles */
.pages-mine {
	background-color: #f8f8f8;
	min-height: 100vh;
	padding-bottom: 120rpx;

	.header {
		height: 292rpx;
		background-color: #599EFF;
		position: relative;

		.header-content {
			display: flex;
			align-items: center;
			padding: 40rpx 30rpx 0;

			.avatar_view {
				width: 120rpx;
				height: 120rpx;
				border-radius: 50%;
				overflow: hidden;
				box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

				.avatar {
					width: 100%;
					height: 100%;
					border-radius: 50%;
				}
			}

			.user-info {
				flex: 1;
				margin-left: 20rpx;
				color: #fff;

				.user-info-logged {
					display: flex;
					flex-direction: column;
					gap: 10rpx;
				}

				.nickname {
					font-size: 36rpx;
					font-weight: bold;
				}

				.phone-number {
					font-size: 28rpx;
					opacity: 0.9;
				}

				button {
					background: none;
					border: 2rpx solid rgba(255, 255, 255, 0.5);
					border-radius: 32rpx;
					color: #fff;
					font-size: 32rpx;
					line-height: 1.5;
					padding: 10rpx 30rpx;

					&.loading {
						opacity: 0.7;
					}

					&::after {
						border: none;
					}
				}

				.status-info-container {
					display: flex;
					align-items: center;
					gap: 20rpx;
					margin-top: 10rpx;
					flex-wrap: wrap;
				}

				.status-badge {
					display: inline-block;
					padding: 8rpx 20rpx;
					font-size: 24rpx;
					line-height: 1.2;
					border-radius: 20rpx;
					color: #fff;
					text-align: center;
					width: fit-content;
					box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
					cursor: pointer;
				}

				.status-not-registered {
					background-color: #b0b0b0;
				}

				.status-pending {
					background-color: #f4b400;
				}

				.status-approved {
					background-color: #f5a623;
				}

				.status-rejected {
					background-color: #f44336;
				}

				.master-info {
					display: flex;
					align-items: center;
					gap: 12rpx;
					font-size: 22rpx;
				}

				.credit-info-badge {
					display: flex;
					align-items: center;
					gap: 6rpx;
					padding: 8rpx 20rpx;
					// background-color: #f5a623;
					border-radius: 20rpx;
					color: #fff;
					font-size: 24rpx;
					line-height: 1.2;
					text-align: center;
					width: fit-content;
					box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
					cursor: pointer;
					transition: all 0.3s ease;

					&:hover {
						transform: translateY(-1rpx);
						box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
					}

					.credit-label {
						font-size: 22rpx;
						opacity: 0.9;
					}

					.credit-value {
						font-weight: bold;
						font-size: 24rpx;
					}
				}

				.star-info-badge {
					display: flex;
					align-items: center;
					gap: 4rpx;
					padding: 8rpx 20rpx;
					// background-color: #f4b400;
					border-radius: 20rpx;
					color: #fff;
					font-size: 24rpx;
					line-height: 1.2;
					text-align: center;
					width: fit-content;
					box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
					cursor: pointer;
					transition: all 0.3s ease;

					&:hover {
						transform: translateY(-1rpx);
						box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
					}

					.star-icon {
						font-size: 22rpx;
					}

					.star-value {
						font-weight: bold;
						font-size: 24rpx;
					}
				}
			}

			.settings {
				padding: 10rpx;

				.icon-xitong {
					font-size: 40rpx;
					color: #fff;
				}
			}
		}
	}

	.box1 {
		margin-top: -20rpx;
		border-radius: 36rpx 36rpx 0 0;
		position: relative;
		z-index: 10;
	}

	.mine-menu-list {
		background-color: #fff;
		margin: 0 20rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
		margin-bottom: 20rpx;

		.menu-title {
			height: 90rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 30rpx 0 40rpx;
			border-bottom: 1px solid #f0f0f0;

			.f-paragraph {
				font-size: 32rpx;
				color: #333;
				font-weight: bold;
			}
		}

		.flex-warp {
			display: flex;
			flex-wrap: wrap;
			padding: 30rpx 0;

			.order-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				width: 25%;
				font-size: 25rpx;
				margin-top: 20rpx;
				color: #666;
				transition: transform 0.2s;

				&:active {
					transform: scale(0.95);
				}

				.icon-container {
					position: relative;
					display: flex;
					align-items: center;
					justify-content: center;
				}

				.number-circle {
					position: absolute;
					top: -10rpx;
					right: -5rpx;
					width: 30rpx;
					height: 30rpx;
					background-color: #ff4d4f;
					color: #fff;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 20rpx;
					font-weight: bold;
				}
			}

			.mt-sm {
				margin-top: 16rpx;
			}
		}
	}

	.spacer {
		height: 20rpx;
		background-color: transparent;
	}

	.mine-tool-grid {
		background-color: #fff;
		margin: 0 20rpx 30rpx;
		border-radius: 12rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
		padding: 30rpx;

		.grid-container {
			display: flex;
			flex-wrap: wrap;
			justify-content: flex-start;
			gap: 20rpx;
		}

		.grid-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			width: calc(33.33% - 20rpx);
			min-width: 140rpx;
			transition: transform 0.2s ease;

			&:active {
				transform: scale(0.95);
			}

			.grid-icon-container {
				width: 80rpx;
				height: 80rpx;
				border-radius: 20rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				position: relative;

				&.switch-identity {
					/* Specific styling for switch-identity icon */
				}
			}

			.grid-text {
				font-size: 25rpx;
				color: #333;
				font-weight: 500;
				text-align: center;
				line-height: 1.2;
				margin-bottom: 8rpx;
			}

			.contact-btn-wrapper {
				background: none;
				border: none;
				padding: 0;
				margin: 0;
				display: flex;
				flex-direction: column;
				align-items: center;
				line-height: 1;

				&::after {
					border: none;
				}
			}
		}
	}

	.flex-between {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
}

/* 驳回原因弹窗样式 */
.reject-reason-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 2000;
	display: flex;
	align-items: center;
	justify-content: center;
}

.reject-reason-popup {
	background-color: #fff;
	width: 80%;
	max-width: 600rpx;
	border-radius: 20rpx;
	position: relative;
	animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
	from {
		opacity: 0;
		transform: scale(0.9);
	}

	to {
		opacity: 1;
		transform: scale(1);
	}
}

.popup-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	text-align: center;
	margin-bottom: 30rpx;
}

.reject-reason-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
	padding: 20rpx;
	background-color: #f8f8f8;
	border-radius: 10rpx;
	margin-bottom: 30rpx;
	min-height: 100rpx;
}

.confirm-btn {
	width: 100%;
	height: 80rpx;
	background-color: #448cfb;
	color: #fff;
	border: none;
	border-radius: 10rpx;
	font-size: 28rpx;
	font-weight: bold;

	&::after {
		border: none;
	}
}
</style>
```